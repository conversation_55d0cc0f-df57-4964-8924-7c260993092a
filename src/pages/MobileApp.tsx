import { useState } from "react";
import { DragDropContext, Droppable, DropResult } from "react-beautiful-dnd";
import { MobileScreen } from "@/components/mobile/MobileScreen";
import {
  DeviceSelector,
  DEVICE_MODELS,
  DeviceModel,
} from "@/components/mobile/DeviceSelector";
import { CategoryIcon } from "@/components/mobile/CategoryIcon";
import { FlashSale } from "@/components/mobile/FlashSale";
import { ProductCard } from "@/components/mobile/ProductCard";
import { BannerCard } from "@/components/mobile/BannerCard";
import { DraggableSection } from "@/components/mobile/DraggableSection";
import { BottomNavigation } from "@/components/mobile/BottomNavigation";
import { useToast } from "@/hooks/use-toast";

interface AppSection {
  id: string;
  type: string;
  content: any;
}

const MobileApp = () => {
  const { toast } = useToast();

  const [selectedPlatform, setSelectedPlatform] = useState<
    "iphone" | "android"
  >("iphone");
  const [selectedModel, setSelectedModel] = useState<string>("iphone-15-pro");

  const getCurrentDevice = (): DeviceModel => {
    const models = DEVICE_MODELS[selectedPlatform];
    return (
      models.find((model: DeviceModel) => model.id === selectedModel) ||
      models[0]
    );
  };

  const handlePlatformChange = (platform: "iphone" | "android") => {
    setSelectedPlatform(platform);
    setSelectedModel(DEVICE_MODELS[platform][0].id);
  };

  const [sections, setSections] = useState<AppSection[]>([
    {
      id: "categories-1",
      type: "categories",
      content: {
        items: [
          { icon: "🍼", title: "Sữa" },
          { icon: "👶", title: "Bỉm tã" },
          { icon: "🧸", title: "Đồ chơi" },
          { icon: "💄", title: "Mỹ phẩm" },
          { icon: "🍎", title: "Thực phẩm" },
        ],
      },
    },
    {
      id: "categories-2",
      type: "categories",
      content: {
        items: [
          { icon: "🚗", title: "Xe đẩy" },
          { icon: "👕", title: "Quần áo" },
          { icon: "🍼", title: "Bình sữa" },
          { icon: "📱", title: "Thiết bị" },
          { icon: "📦", title: "Tất cả" },
        ],
      },
    },
    {
      id: "flash-sale",
      type: "flash-sale",
      content: {},
    },
    {
      id: "products-1",
      type: "products",
      content: {
        items: [
          {
            id: "p1",
            image: "/lovable-uploads/c8090615-afb2-4186-9fcb-b5453f8d73a4.png",
            title: "Sữa bột Nestilac Pedia Ion 800g",
            price: "485.000đ",
            originalPrice: "520.000đ",
            discount: "Giảm 5%",
            rating: 5,
            sold: 0,
          },
          {
            id: "p2",
            image: "/lovable-uploads/c8090615-afb2-4186-9fcb-b5453f8d73a4.png",
            title: "Quần áo trẻ em mới",
            price: "140.000đ",
            originalPrice: "180.000đ",
            discount: "Giảm 22%",
            rating: 5,
            sold: 0,
          },
          {
            id: "p3",
            image: "/lovable-uploads/c8090615-afb2-4186-9fcb-b5453f8d73a4.png",
            title: "Sữa bột Grow Gold 1800g",
            price: "180.000đ",
            originalPrice: "220.000đ",
            discount: "Giảm 18%",
            rating: 5,
            sold: 0,
          },
        ],
      },
    },
    {
      id: "banners-1",
      type: "banners",
      content: {
        items: [
          {
            id: "b1",
            title: "Ưu đãi lên đến 100K",
            subtitle: "Cho đơn hàng đầu tiên",
            image: "/lovable-uploads/c8090615-afb2-4186-9fcb-b5453f8d73a4.png",
            backgroundColor: "bg-gradient-to-r from-pink-100 to-pink-200",
          },
          {
            id: "b2",
            title: "Vitamin cho mẹ và bé",
            subtitle: "Giảm giá 15%",
            image: "/lovable-uploads/c8090615-afb2-4186-9fcb-b5453f8d73a4.png",
            backgroundColor: "bg-gradient-to-r from-yellow-100 to-orange-200",
          },
        ],
      },
    },
  ]);

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    const items = Array.from(sections);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    setSections(items);

    toast({
      title: "Section đã được di chuyển!",
      description: `${result.draggableId} đã được di chuyển từ vị trí ${
        result.source.index + 1
      } sang vị trí ${result.destination.index + 1}`,
    });
  };

  const bottomNavItems = [
    { id: "home", icon: "🏠", label: "Trang chủ", active: true },
    { id: "category", icon: "📂", label: "Danh mục", active: false },
    { id: "search", icon: "🔍", label: "Tìm kiếm", active: false },
    { id: "cart", icon: "🛒", label: "Giỏ hàng", active: false },
    { id: "profile", icon: "👤", label: "Tài khoản", active: false },
  ];

  const renderSection = (section: AppSection, index: number) => {
    switch (section.type) {
      case "categories":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="grid grid-cols-5 gap-2 px-4 mb-4">
              {section.content.items.map((item: any, idx: number) => (
                <CategoryIcon key={idx} icon={item.icon} title={item.title} />
              ))}
            </div>
          </DraggableSection>
        );

      case "flash-sale":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <FlashSale className="mb-4" />
          </DraggableSection>
        );

      case "products":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="px-4 mb-4">
              <div className="grid grid-cols-3 gap-3">
                {section.content.items.map((product: any) => (
                  <ProductCard
                    key={product.id}
                    image={product.image}
                    title={product.title}
                    price={product.price}
                    originalPrice={product.originalPrice}
                    discount={product.discount}
                    rating={product.rating}
                    sold={product.sold}
                  />
                ))}
              </div>
            </div>
          </DraggableSection>
        );

      case "banners":
        return (
          <DraggableSection key={section.id} id={section.id} index={index}>
            <div className="px-4 mb-4 space-y-3">
              {section.content.items.map((banner: any) => (
                <BannerCard
                  key={banner.id}
                  title={banner.title}
                  subtitle={banner.subtitle}
                  image={banner.image}
                  backgroundColor={banner.backgroundColor}
                />
              ))}
            </div>
          </DraggableSection>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-muted/30 ">
      <div className="max-w-6xl mx-auto">
        <DeviceSelector
          selectedPlatform={selectedPlatform}
          selectedModel={selectedModel}
          onPlatformChange={handlePlatformChange}
          onModelChange={setSelectedModel}
        />

        <div className="flex justify-center">
          <MobileScreen deviceModel={getCurrentDevice()}>
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="mobile-app">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="h-full overflow-y-auto pb-16 scrollbar-hide"
                    style={{
                      scrollbarWidth: "none",
                      msOverflowStyle: "none",
                    }}
                  >
                    {/* Scrollable Content */}
                    <div className="pt-4">
                      {sections.map((section, index) =>
                        renderSection(section, index)
                      )}
                      {provided.placeholder}
                    </div>
                  </div>
                )}
              </Droppable>
            </DragDropContext>

            {/* Bottom Navigation */}
            <BottomNavigation
              items={bottomNavItems}
              onItemClick={(id) => console.log("Nav clicked:", id)}
            />
          </MobileScreen>
        </div>
      </div>
    </div>
  );
};

export default MobileApp;
