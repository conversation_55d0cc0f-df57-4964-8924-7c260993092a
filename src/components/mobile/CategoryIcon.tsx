import React from 'react';
import { cn } from '@/lib/utils';

interface CategoryIconProps {
  icon: string;
  title: string;
  className?: string;
  onClick?: () => void;
}

export const CategoryIcon: React.FC<CategoryIconProps> = ({
  icon,
  title,
  className,
  onClick
}) => {
  return (
    <div 
      className={cn(
        "flex flex-col items-center gap-2 p-2 cursor-pointer",
        "hover:bg-muted/50 rounded-lg transition-colors",
        className
      )}
      onClick={onClick}
    >
      <div className="w-14 h-14 bg-mobile-category-icon rounded-xl flex items-center justify-center shadow-sm border border-border/50">
        <span className="text-2xl">{icon}</span>
      </div>
      <span className="text-xs text-foreground font-medium text-center leading-tight">
        {title}
      </span>
    </div>
  );
};