import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface ProductCardProps {
  image: string;
  title: string;
  price: string;
  originalPrice?: string;
  discount?: string;
  rating?: number;
  sold?: number;
  className?: string;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  image,
  title,
  price,
  originalPrice,
  discount,
  rating = 5,
  sold = 0,
  className
}) => {
  return (
    <div className={cn(
      "bg-card rounded-xl p-3 shadow-sm border border-border/50",
      "hover:shadow-md transition-shadow duration-200",
      "relative overflow-hidden",
      className
    )}>
      {/* Discount Badge */}
      {discount && (
        <div className="absolute top-2 left-2 z-10">
          <div className="bg-mobile-discount-badge text-white px-2 py-1 rounded-md text-xs font-bold">
            {discount}
          </div>
        </div>
      )}

      {/* Product Image */}
      <div className="aspect-square bg-muted rounded-lg mb-3 overflow-hidden">
        <img 
          src={image} 
          alt={title}
          className="w-full h-full object-cover"
        />
      </div>

      {/* Product Info */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-foreground line-clamp-2 leading-tight">
          {title}
        </h3>
        
        <div className="flex items-baseline gap-1">
          <span className="text-mobile-price-text font-bold text-base">
            {price}
          </span>
          {originalPrice && (
            <span className="text-muted-foreground text-xs line-through">
              {originalPrice}
            </span>
          )}
        </div>

        {/* Rating and Sold */}
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <span className="text-yellow-500">⭐</span>
            <span>{rating}</span>
            <span>Đã bán {sold}</span>
          </div>
        </div>

        {/* Add to Cart Button */}
        <Button size="sm" className="w-full text-xs h-8 bg-primary hover:bg-primary-light">
          🛒
        </Button>
      </div>
    </div>
  );
};