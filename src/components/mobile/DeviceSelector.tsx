import React from "react";
import {
  Select,
  SelectContent,
  <PERSON>I<PERSON>,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export interface DeviceModel {
  id: string;
  name: string;
  width: number;
  height: number;
  borderRadius: string;
  statusBarHeight: number;
  dynamicIsland?: {
    enabled: boolean;
    width: number;
    height: number;
    offsetY: number;
    backgroundColor: string;
  };
  notch?: {
    enabled: boolean;
    width: number;
    height: number;
    offsetY: number;
    backgroundColor: string;
    dynamicIsland?: boolean;
  };
  bezelSize: number;
  deviceDimensions: { width: number; height: number; depth: number };
  material: string;
  weight: number;
  colors: string[];
  processor: {
    chip: string;
    cores: number;
    gpu: string;
    neuralEngine: string;
    clockSpeed?: string;
  };
  storageOptions: number[];
  display: {
    type: string;
    technology: string;
    refreshRate: string;
    pixelDensity: number;
    size?: number;
    resolution?: string;
    safeAreaInsets: {
      top: number;
      bottom: number;
      left: number;
      right: number;
    };
    homeIndicator: {
      height: number;
      width: number;
      color: string;
      borderRadius: string;
    } | null;
  };
  buttons: {
    right: Array<{
      type: string;
      offsetY: number;
      height: number;
      width: number;
      color: string;
      borderRadius: string;
    }>;
    left: Array<{
      type: string;
      offsetY: number;
      height: number;
      width: number;
      color: string;
      borderRadius: string;
    }>;
    bottom?: Array<{
      type: string;
      shape?: string;
      size?: number;
      offsetY: number;
      color: string;
      borderWidth?: number;
      borderColor?: string;
    }>;
  };
  camera: {
    position: string;
    layout: string;
    size: number;
    offset: { x: number; y: number };
    lenses: Array<{
      type: string;
      diameter: number;
      offset: { x: number; y: number };
      megapixels: number;
      aperture: string;
    }>;
    flash: { diameter: number; offset: { x: number; y: number }; type: string };
    features: string[];
  };
  battery: {
    capacity: string;
    wirelessCharging: boolean;
    fastCharging: string;
  };
  connectivity: {
    usb: string;
    wireless: string[];
  };
  screenToBodyRatio: number;
  shadow: string;
  backgroundImage: string;
  ipRating?: string;
  software?: {
    os: string;
    ui: string;
    updates: string;
  };
  additionalFeatures?: string[];
}

export const DEVICE_MODELS = {
  iphone: [
    {
      id: "iphone-15-pro",
      name: "iPhone 15 Pro",
      width: 375,
      height: 812,
      borderRadius: "rounded-[2.5rem]",
      statusBarHeight: 44,
      dynamicIsland: {
        enabled: true,
        width: 100,
        height: 30,
        offsetY: 12,
        backgroundColor: "#000000",
      },
      bezelSize: 1.5,
      deviceDimensions: { width: 393, height: 852, depth: 8.25 },
      material: "Titanium",
      weight: 187,
      colors: ["#1C2526", "#D4A017", "#F5F5F5", "#2A2A2A"],
      processor: {
        chip: "A17 Pro",
        cores: 6,
        gpu: "6-core GPU",
        neuralEngine: "16-core",
      },
      storageOptions: [128, 256, 512, 1000],
      display: {
        type: "Super Retina XDR",
        technology: "OLED",
        refreshRate: "120Hz",
        pixelDensity: 460,
        safeAreaInsets: { top: 59, bottom: 34, left: 0, right: 0 },
        homeIndicator: {
          height: 4,
          width: 134,
          color: "#FFFFFF",
          borderRadius: "rounded-full",
        },
      },
      buttons: {
        right: [
          {
            type: "volume-up",
            offsetY: 150,
            height: 40,
            width: 5,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
          {
            type: "volume-down",
            offsetY: 200,
            height: 40,
            width: 5,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
          {
            type: "action",
            offsetY: 300,
            height: 30,
            width: 5,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
        ],
        left: [
          {
            type: "power",
            offsetY: 150,
            height: 50,
            width: 5,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
        ],
      },
      camera: {
        position: "top-left",
        layout: "diagonal",
        size: 50,
        offset: { x: 10, y: 10 },
        lenses: [
          {
            type: "wide",
            diameter: 12,
            offset: { x: 0, y: 0 },
            megapixels: 48,
            aperture: "f/1.78",
          },
          {
            type: "ultra-wide",
            diameter: 10,
            offset: { x: 15, y: 15 },
            megapixels: 12,
            aperture: "f/2.2",
          },
          {
            type: "telephoto",
            diameter: 10,
            offset: { x: 30, y: 30 },
            megapixels: 12,
            aperture: "f/2.8",
          },
        ],
        flash: {
          diameter: 8,
          offset: { x: 20, y: 20 },
          type: "Adaptive True Tone",
        },
        features: ["Night mode", "Photonic Engine", "Cinematic mode", "ProRAW"],
      },
      battery: {
        capacity: "3274mAh",
        wirelessCharging: true,
        fastCharging: "20W",
      },
      connectivity: {
        usb: "USB-C 3.0",
        wireless: ["5G", "Wi-Fi 6E", "Bluetooth 5.3"],
      },
      screenToBodyRatio: 0.88,
      shadow: "shadow-lg",
      backgroundImage: "linear-gradient(145deg, #1C2526, #2A2A2A)",
    },
    {
      id: "iphone-14",
      name: "iPhone 14",
      width: 375,
      height: 812,
      borderRadius: "rounded-[2.5rem]",
      statusBarHeight: 44,
      notch: {
        enabled: true,
        width: 140,
        height: 36,
        offsetY: 0,
        backgroundColor: "#000000",
      },
      bezelSize: 2.0,
      deviceDimensions: { width: 395, height: 851, depth: 7.8 },
      material: "Aluminum",
      weight: 172,
      colors: ["#000000", "#FF3B30", "#F4F0E6", "#48319D"],
      processor: {
        chip: "A15 Bionic",
        cores: 6,
        gpu: "5-core GPU",
        neuralEngine: "16-core",
      },
      storageOptions: [128, 256, 512],
      display: {
        type: "Super Retina XDR",
        technology: "OLED",
        refreshRate: "60Hz",
        pixelDensity: 460,
        safeAreaInsets: { top: 47, bottom: 34, left: 0, right: 0 },
        homeIndicator: {
          height: 4,
          width: 134,
          color: "#FFFFFF",
          borderRadius: "rounded-full",
        },
      },
      buttons: {
        right: [
          {
            type: "volume-up",
            offsetY: 140,
            height: 40,
            width: 5,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
          {
            type: "volume-down",
            offsetY: 190,
            height: 40,
            width: 5,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
        ],
        left: [
          {
            type: "power",
            offsetY: 140,
            height: 50,
            width: 5,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
        ],
      },
      camera: {
        position: "top-left",
        layout: "vertical",
        size: 45,
        offset: { x: 12, y: 12 },
        lenses: [
          {
            type: "wide",
            diameter: 12,
            offset: { x: 0, y: 0 },
            megapixels: 12,
            aperture: "f/1.5",
          },
          {
            type: "ultra-wide",
            diameter: 10,
            offset: { x: 0, y: 15 },
            megapixels: 12,
            aperture: "f/2.4",
          },
        ],
        flash: { diameter: 8, offset: { x: 15, y: 7 }, type: "True Tone" },
        features: ["Night mode", "Photonic Engine", "Cinematic mode"],
      },
      battery: {
        capacity: "3279mAh",
        wirelessCharging: true,
        fastCharging: "20W",
      },
      connectivity: {
        usb: "Lightning",
        wireless: ["5G", "Wi-Fi 6", "Bluetooth 5.0"],
      },
      screenToBodyRatio: 0.87,
      shadow: "shadow-md",
      backgroundImage: "linear-gradient(145deg, #000000, #333333)",
    },
    {
      id: "iphone-se",
      name: "iPhone SE",
      width: 375,
      height: 667,
      borderRadius: "rounded-2xl",
      statusBarHeight: 20,
      bezelSize: 4.0,
      deviceDimensions: { width: 414, height: 736, depth: 7.6 },
      material: "Aluminum",
      weight: 144,
      colors: ["#000000", "#FFFFFF", "#FF3B30"],
      processor: {
        chip: "A15 Bionic",
        cores: 6,
        gpu: "4-core GPU",
        neuralEngine: "16-core",
      },
      storageOptions: [64, 128, 256],
      display: {
        type: "Retina HD",
        technology: "LCD",
        refreshRate: "60Hz",
        pixelDensity: 326,
        safeAreaInsets: { top: 20, bottom: 20, left: 0, right: 0 },
        homeIndicator: null,
      },
      buttons: {
        right: [],
        left: [
          {
            type: "power",
            offsetY: 100,
            height: 40,
            width: 5,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
        ],
        bottom: [
          {
            type: "home",
            shape: "circle",
            size: 40,
            offsetY: -20,
            color: "#A1A1A6",
            borderWidth: 1,
            borderColor: "#000000",
          },
        ],
      },
      camera: {
        position: "top-left",
        layout: "single",
        size: 30,
        offset: { x: 15, y: 15 },
        lenses: [
          {
            type: "wide",
            diameter: 10,
            offset: { x: 0, y: 0 },
            megapixels: 12,
            aperture: "f/1.8",
          },
        ],
        flash: { diameter: 6, offset: { x: 10, y: 5 }, type: "True Tone" },
        features: ["Smart HDR", "Portrait mode"],
      },
      battery: {
        capacity: "2018mAh",
        wirelessCharging: true,
        fastCharging: "18W",
      },
      connectivity: {
        usb: "Lightning",
        wireless: ["5G", "Wi-Fi 6", "Bluetooth 5.0"],
      },
      screenToBodyRatio: 0.65,
      shadow: "shadow-sm",
      backgroundImage: "linear-gradient(145deg, #000000, #4A4A4A)",
    },
  ],
  android: [
    {
      id: "samsung-galaxy-s25-ultra",
      name: "Samsung Galaxy S25 Ultra",
      width: 412,
      height: 915,
      borderRadius: "rounded-[1.5rem]",
      statusBarHeight: 48,
      notch: {
        enabled: false,
        width: 0,
        height: 0,
        offsetY: 0,
        backgroundColor: "#000000",
        dynamicIsland: false,
      },
      bezelSize: 1.2,
      deviceDimensions: { width: 77.6, height: 162.8, depth: 8.2 },
      material: "Titanium",
      weight: 229,
      colors: ["#1C2526", "#D4A017", "#F5F5F5", "#2A2A2A"],
      processor: {
        chip: "Snapdragon 8 Elite for Galaxy",
        cores: 8,
        gpu: "Adreno 830",
        neuralEngine: "Hexagon NPU",
        clockSpeed: "4.47GHz",
      },
      storageOptions: [256, 512, 1024],
      display: {
        type: "Dynamic AMOLED 2X",
        technology: "AMOLED",
        refreshRate: "120Hz",
        pixelDensity: 501,
        size: 6.9,
        resolution: "1440 x 3088",
        safeAreaInsets: { top: 48, bottom: 36, left: 0, right: 0 },
        homeIndicator: {
          height: 4,
          width: 140,
          color: "#FFFFFF",
          borderRadius: "rounded-full",
        },
      },
      buttons: {
        right: [
          {
            type: "volume-up",
            offsetY: 160,
            height: 38,
            width: 6,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
          {
            type: "volume-down",
            offsetY: 210,
            height: 38,
            width: 6,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
          {
            type: "power",
            offsetY: 300,
            height: 50,
            width: 6,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
        ],
        left: [],
      },
      camera: {
        position: "top-left",
        layout: "vertical",
        size: 60,
        offset: { x: 12, y: 12 },
        lenses: [
          {
            type: "main",
            diameter: 14,
            offset: { x: 0, y: 0 },
            megapixels: 200,
            aperture: "f/1.7",
          },
          {
            type: "ultra-wide",
            diameter: 12,
            offset: { x: 0, y: 20 },
            megapixels: 50,
            aperture: "f/2.2",
          },
          {
            type: "telephoto",
            diameter: 12,
            offset: { x: 0, y: 40 },
            megapixels: 10,
            aperture: "f/2.4",
          },
          {
            type: "periscope-telephoto",
            diameter: 12,
            offset: { x: 0, y: 60 },
            megapixels: 10,
            aperture: "f/4.9",
          },
        ],
        flash: {
          diameter: 8,
          offset: { x: 15, y: 30 },
          type: "Adaptive True Tone",
        },
        features: ["Galaxy AI", "Nightography", "8K Video", "ProVisual Engine"],
      },
      battery: {
        capacity: "5000mAh",
        wirelessCharging: true,
        fastCharging: "45W",
      },
      connectivity: {
        usb: "USB-C 3.2 Gen 1",
        wireless: ["5G", "Wi-Fi 7", "Bluetooth 5.3", "UWB"],
      },
      screenToBodyRatio: 0.91,
      shadow: "shadow-xl",
      backgroundImage: "linear-gradient(145deg, #1C2526, #2A2A2A)",
      ipRating: "IP68",
      software: {
        os: "Android 15",
        ui: "One UI 8",
        updates: "7 years",
      },
      additionalFeatures: ["S Pen", "Gorilla Armor 2", "AI Select Tool"],
    },
    {
      id: "google-pixel-9-pro",
      name: "Google Pixel 9 Pro",
      width: 411,
      height: 896,
      borderRadius: "rounded-[1.8rem]",
      statusBarHeight: 44,
      notch: {
        enabled: false,
        width: 0,
        height: 0,
        offsetY: 0,
        backgroundColor: "#000000",
        dynamicIsland: false,
      },
      bezelSize: 1.5,
      deviceDimensions: { width: 76.6, height: 162.7, depth: 8.5 },
      material: "Aluminum",
      weight: 199,
      colors: ["#000000", "#D3D3D3", "#228B22", "#FFF8DC"],
      processor: {
        chip: "Tensor G4",
        cores: 8,
        gpu: "Mali-G715",
        neuralEngine: "Titan M2",
        clockSpeed: "3.1GHz",
      },
      storageOptions: [128, 256, 512, 1024],
      display: {
        type: "Super Actua",
        technology: "OLED",
        refreshRate: "120Hz",
        pixelDensity: 495,
        size: 6.3,
        resolution: "1280 x 2856",
        safeAreaInsets: { top: 44, bottom: 34, left: 0, right: 0 },
        homeIndicator: {
          height: 4,
          width: 134,
          color: "#FFFFFF",
          borderRadius: "rounded-full",
        },
      },
      buttons: {
        right: [
          {
            type: "power",
            offsetY: 150,
            height: 50,
            width: 6,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
        ],
        left: [
          {
            type: "volume-up",
            offsetY: 140,
            height: 38,
            width: 6,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
          {
            type: "volume-down",
            offsetY: 190,
            height: 38,
            width: 6,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
        ],
      },
      camera: {
        position: "top-center",
        layout: "horizontal",
        size: 55,
        offset: { x: 15, y: 15 },
        lenses: [
          {
            type: "main",
            diameter: 14,
            offset: { x: 0, y: 0 },
            megapixels: 50,
            aperture: "f/1.68",
          },
          {
            type: "ultra-wide",
            diameter: 12,
            offset: { x: 20, y: 0 },
            megapixels: 48,
            aperture: "f/1.7",
          },
          {
            type: "telephoto",
            diameter: 12,
            offset: { x: 40, y: 0 },
            megapixels: 48,
            aperture: "f/2.8",
          },
        ],
        flash: { diameter: 8, offset: { x: 30, y: 10 }, type: "Dual-LED" },
        features: ["Pixel Studio", "Add Me", "Night Sight", "Video Boost"],
      },
      battery: {
        capacity: "4700mAh",
        wirelessCharging: true,
        fastCharging: "27W",
      },
      connectivity: {
        usb: "USB-C 3.2 Gen 2",
        wireless: ["5G", "Wi-Fi 6E", "Bluetooth 5.3", "UWB"],
      },
      screenToBodyRatio: 0.89,
      shadow: "shadow-lg",
      backgroundImage: "linear-gradient(145deg, #000000, #333333)",
      ipRating: "IP68",
      software: {
        os: "Android 15",
        ui: "Stock Android",
        updates: "7 years",
      },
      additionalFeatures: ["Gemini Live", "Call Screen", "Pixel Screenshots"],
    },
    {
      id: "oneplus-13",
      name: "OnePlus 13",
      width: 408,
      height: 900,
      borderRadius: "rounded-[1.7rem]",
      statusBarHeight: 46,
      notch: {
        enabled: false,
        width: 0,
        height: 0,
        offsetY: 0,
        backgroundColor: "#000000",
        dynamicIsland: false,
      },
      bezelSize: 1.3,
      deviceDimensions: { width: 75.3, height: 162.8, depth: 8.9 },
      material: "Aluminum",
      weight: 210,
      colors: ["#000000", "#2E8B57", "#F5F5F5"],
      processor: {
        chip: "Snapdragon 8 Elite",
        cores: 8,
        gpu: "Adreno 830",
        neuralEngine: "Hexagon NPU",
        clockSpeed: "4.47GHz",
      },
      storageOptions: [256, 512, 1024],
      display: {
        type: "AMOLED",
        technology: "AMOLED",
        refreshRate: "120Hz",
        pixelDensity: 510,
        size: 6.8,
        resolution: "1440 x 3168",
        safeAreaInsets: { top: 46, bottom: 34, left: 0, right: 0 },
        homeIndicator: {
          height: 4,
          width: 134,
          color: "#FFFFFF",
          borderRadius: "rounded-full",
        },
      },
      buttons: {
        right: [
          {
            type: "power",
            offsetY: 150,
            height: 50,
            width: 6,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
          {
            type: "alert-slider",
            offsetY: 250,
            height: 30,
            width: 6,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
        ],
        left: [
          {
            type: "volume-up",
            offsetY: 140,
            height: 38,
            width: 6,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
          {
            type: "volume-down",
            offsetY: 190,
            height: 38,
            width: 6,
            color: "#A1A1A6",
            borderRadius: "rounded-md",
          },
        ],
      },
      camera: {
        position: "top-left",
        layout: "circular",
        size: 58,
        offset: { x: 10, y: 10 },
        lenses: [
          {
            type: "main",
            diameter: 14,
            offset: { x: 0, y: 0 },
            megapixels: 50,
            aperture: "f/1.6",
          },
          {
            type: "ultra-wide",
            diameter: 12,
            offset: { x: 20, y: 20 },
            megapixels: 50,
            aperture: "f/2.0",
          },
          {
            type: "telephoto",
            diameter: 12,
            offset: { x: 40, y: 40 },
            megapixels: 50,
            aperture: "f/2.6",
          },
        ],
        flash: { diameter: 8, offset: { x: 30, y: 30 }, type: "LED" },
        features: ["Hasselblad Color Calibration", "Nightscape", "8K Video"],
      },
      battery: {
        capacity: "6000mAh",
        wirelessCharging: true,
        fastCharging: "100W",
      },
      connectivity: {
        usb: "USB-C 3.2 Gen 1",
        wireless: ["5G", "Wi-Fi 7", "Bluetooth 5.4"],
      },
      screenToBodyRatio: 0.9,
      shadow: "shadow-lg",
      backgroundImage: "linear-gradient(145deg, #000000, #4A4A4A)",
      ipRating: "IP69",
      software: {
        os: "Android 15",
        ui: "OxygenOS 15",
        updates: "5 years",
      },
      additionalFeatures: ["Alert Slider", "Hasselblad Camera System"],
    },
  ],
};

interface DeviceSelectorProps {
  selectedPlatform: "iphone" | "android";
  selectedModel: string;
  onPlatformChange: (platform: "iphone" | "android") => void;
  onModelChange: (modelId: string) => void;
}

export const DeviceSelector: React.FC<DeviceSelectorProps> = ({
  selectedPlatform,
  selectedModel,
  onPlatformChange,
  onModelChange,
}) => {
  const availableModels = DEVICE_MODELS[selectedPlatform];

  return (
    <div className="flex gap-4 mb-6 rounded-lg border">
      <div className="flex-1">
        <Select value={selectedPlatform} onValueChange={onPlatformChange}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="iphone">iPhone</SelectItem>
            <SelectItem value="android">Android</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex-1">
        <Select value={selectedModel} onValueChange={onModelChange}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {availableModels.map((model: DeviceModel) => (
              <SelectItem key={model.id} value={model.id}>
                {model.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
