import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export interface DeviceModel {
  id: string;
  name: string;
  width: number;
  height: number;
  borderRadius: string;
  statusBarHeight: number;
  notch?: boolean;
  dynamicIsland?: boolean;
}

export const DEVICE_MODELS = {
  iphone: [
    {
      id: "iphone-15-pro",
      name: "iPhone 15 Pro",
      width: 320,
      height: 650,
      borderRadius: "rounded-[2.5rem]",
      statusBarHeight: 44,
      dynamicIsland: true,
    },
    {
      id: "iphone-14",
      name: "iPhone 14",
      width: 375,
      height: 812,
      borderRadius: "rounded-[2.5rem]",
      statusBarHeight: 44,
      notch: true,
    },
    {
      id: "iphone-se",
      name: "iPhone SE",
      width: 375,
      height: 667,
      borderRadius: "rounded-2xl",
      statusBarHeight: 20,
    },
  ],
  android: [
    {
      id: "samsung-s24",
      name: "Samsung Galaxy S24",
      width: 375,
      height: 812,
      borderRadius: "rounded-3xl",
      statusBarHeight: 24,
    },
    {
      id: "pixel-8",
      name: "Google Pixel 8",
      width: 375,
      height: 812,
      borderRadius: "rounded-2xl",
      statusBarHeight: 24,
    },
    {
      id: "xiaomi-14",
      name: "Xiaomi 14",
      width: 375,
      height: 812,
      borderRadius: "rounded-3xl",
      statusBarHeight: 28,
    },
  ],
};

interface DeviceSelectorProps {
  selectedPlatform: "iphone" | "android";
  selectedModel: string;
  onPlatformChange: (platform: "iphone" | "android") => void;
  onModelChange: (modelId: string) => void;
}

export const DeviceSelector: React.FC<DeviceSelectorProps> = ({
  selectedPlatform,
  selectedModel,
  onPlatformChange,
  onModelChange,
}) => {
  const availableModels = DEVICE_MODELS[selectedPlatform];

  return (
    <div className="flex gap-4 mb-6 rounded-lg border">
      <div className="flex-1">
        <Select value={selectedPlatform} onValueChange={onPlatformChange}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="iphone">iPhone</SelectItem>
            <SelectItem value="android">Android</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex-1">
        <Select value={selectedModel} onValueChange={onModelChange}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {availableModels.map((model) => (
              <SelectItem key={model.id} value={model.id}>
                {model.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
