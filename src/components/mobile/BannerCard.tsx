import React from 'react';
import { cn } from '@/lib/utils';

interface BannerCardProps {
  title: string;
  subtitle?: string;
  image: string;
  backgroundColor?: string;
  textColor?: string;
  className?: string;
}

export const BannerCard: React.FC<BannerCardProps> = ({
  title,
  subtitle,
  image,
  backgroundColor = 'bg-gradient-to-r from-accent to-accent/80',
  textColor = 'text-accent-foreground',
  className
}) => {
  return (
    <div className={cn(
      "relative rounded-xl overflow-hidden h-24 flex items-center",
      backgroundColor,
      className
    )}>
      <div className="flex-1 p-4 z-10">
        <h3 className={cn("font-bold text-lg leading-tight", textColor)}>
          {title}
        </h3>
        {subtitle && (
          <p className={cn("text-sm opacity-80", textColor)}>
            {subtitle}
          </p>
        )}
      </div>
      
      <div className="absolute right-0 top-0 h-full w-20 opacity-50">
        <img 
          src={image} 
          alt={title}
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  );
};