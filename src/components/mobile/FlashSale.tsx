import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface FlashSaleProps {
  className?: string;
}

export const FlashSale: React.FC<FlashSaleProps> = ({ className }) => {
  const [timeLeft, setTimeLeft] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        let { hours, minutes, seconds } = prev;
        
        if (seconds > 0) {
          seconds--;
        } else if (minutes > 0) {
          minutes--;
          seconds = 59;
        } else if (hours > 0) {
          hours--;
          minutes = 59;
          seconds = 59;
        } else {
          // Reset to a new countdown
          hours = 23;
          minutes = 59;
          seconds = 59;
        }
        
        return { hours, minutes, seconds };
      });
    }, 1000);

    // Initialize with some time
    setTimeLeft({ hours: 23, minutes: 45, seconds: 30 });

    return () => clearInterval(timer);
  }, []);

  const formatTime = (time: number) => time.toString().padStart(2, '0');

  return (
    <div className={cn(
      "flex items-center justify-between p-4 mx-4 rounded-xl",
      "bg-gradient-to-r from-mobile-flash-sale-bg to-mobile-flash-sale-bg/80",
      "border border-mobile-flash-sale/20",
      className
    )}>
      <div className="flex items-center gap-2">
        <span className="text-mobile-flash-sale font-bold text-lg">⚡</span>
        <span className="text-mobile-flash-sale font-bold text-base">Flash Sale</span>
      </div>
      
      <div className="flex items-center gap-2">
        <div className="flex gap-1">
          <div className="bg-mobile-flash-sale text-white px-2 py-1 rounded text-sm font-bold min-w-[28px] text-center">
            {formatTime(timeLeft.hours)}
          </div>
          <div className="bg-mobile-flash-sale text-white px-2 py-1 rounded text-sm font-bold min-w-[28px] text-center">
            {formatTime(timeLeft.minutes)}
          </div>
          <div className="bg-mobile-flash-sale text-white px-2 py-1 rounded text-sm font-bold min-w-[28px] text-center">
            {formatTime(timeLeft.seconds)}
          </div>
        </div>
        <span className="text-mobile-flash-sale text-sm font-medium">Xem tất cả</span>
      </div>
    </div>
  );
};