import React from "react";
import { cn } from "@/lib/utils";
import { DeviceModel } from "./DeviceSelector";

interface MobileScreenProps {
  children: React.ReactNode;
  className?: string;
  deviceModel?: DeviceModel;
}

export const MobileScreen: React.FC<MobileScreenProps> = ({
  children,
  className,
  deviceModel,
}) => {
  const device = deviceModel || {
    width: 375,
    height: 800,
    borderRadius: "rounded-3xl",
    statusBarHeight: 24,
  };

  const renderStatusBar = () => {
    if (deviceModel?.dynamicIsland) {
      return (
        <div className="h-11 bg-foreground flex items-center justify-center relative">
          <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-32 h-7 bg-black rounded-full"></div>
        </div>
      );
    }

    if (deviceModel?.notch) {
      return (
        <div className="h-11 bg-foreground flex items-center justify-center relative">
          <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-40 h-7 bg-black rounded-b-2xl"></div>
        </div>
      );
    }

    return (
      <div
        className={`h-6 bg-foreground ${
          device.borderRadius.includes("2.5rem")
            ? "rounded-t-[2.5rem]"
            : device.borderRadius.includes("3xl")
            ? "rounded-t-3xl"
            : "rounded-t-2xl"
        } flex items-center justify-center`}
      >
        <div className="w-16 h-1 bg-background/30 rounded-full"></div>
      </div>
    );
  };

  return (
    <div
      className={cn(
        "mx-auto bg-mobile-header",
        "border border-border overflow-hidden",
        "relative",
        device.borderRadius,
        className
      )}
      style={{
        width: `${device.width}px`,
        height: `${device.height}px`,
      }}
    >
      {/* Status Bar */}
      {renderStatusBar()}

      {/* Screen Content */}
      <div
        className="bg-background overflow-hidden"
        style={{
          height: `${device.height - device.statusBarHeight}px`,
        }}
      >
        {children}
      </div>
    </div>
  );
};
