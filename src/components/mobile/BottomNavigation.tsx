import React from 'react';
import { cn } from '@/lib/utils';

interface NavItem {
  id: string;
  icon: string;
  label: string;
  active?: boolean;
}

interface BottomNavigationProps {
  items: NavItem[];
  onItemClick?: (id: string) => void;
  className?: string;
}

export const BottomNavigation: React.FC<BottomNavigationProps> = ({
  items,
  onItemClick,
  className
}) => {
  return (
    <div className={cn(
      "flex items-center justify-around py-2 px-4",
      "bg-mobile-header border-t border-border/50",
      "absolute bottom-0 left-0 right-0 z-20",
      className
    )}>
      {items.map((item) => (
        <button
          key={item.id}
          onClick={() => onItemClick?.(item.id)}
          className={cn(
            "flex flex-col items-center gap-1 p-2 rounded-lg",
            "transition-colors duration-200",
            item.active 
              ? "text-primary" 
              : "text-muted-foreground hover:text-foreground"
          )}
        >
          <span className="text-xl">{item.icon}</span>
          <span className="text-xs font-medium">{item.label}</span>
        </button>
      ))}
    </div>
  );
};